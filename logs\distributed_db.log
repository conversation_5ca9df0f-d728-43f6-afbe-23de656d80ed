2025-05-27 16:34:58,356 - database - ERROR - Database connection Connection pool creation failed for node db1 FAILED - localhost:3306: 1045 (28000): Access denied for user 'root'@'localhost' (using password: YES)
2025-05-27 16:34:58,356 - database - ERROR - Failed to create connection pool for db1: 1045 (28000): Access denied for user 'root'@'localhost' (using password: YES)
2025-05-27 16:35:02,453 - database - ERROR - Database connection Connection pool creation failed for node db2 FAILED - localhost:3307: 2003 (HY000): Can't connect to MySQL server on 'localhost:3307' (10061)
2025-05-27 16:35:02,454 - database - ERROR - Failed to create connection pool for db2: 2003 (HY000): Can't connect to MySQL server on 'localhost:3307' (10061)
2025-05-27 16:35:02,456 - database - INFO - Database manager initialized with nodes: db1, db2
2025-05-27 16:35:02,718 - transaction - INFO - Transaction d312b7ce-f27b-4b4b-b715-63f888c3a109 started with participants: ['participant_1', 'participant_2']
2025-05-27 16:35:02,724 - transaction - INFO - Transaction 720843eb-7bda-46de-944c-4dca40bcba8d started with participants: ['participant_1', 'participant_2']
2025-05-27 16:35:02,727 - transaction - INFO - Transaction 720843eb-7bda-46de-944c-4dca40bcba8d started successfully
2025-05-27 16:35:02,728 - transaction - INFO - Transaction 1aafeb6a-a2fa-40dd-9d8d-c268cfa1cf22 started with participants: ['participant_1', 'participant_2']
2025-05-27 16:35:02,729 - system - ERROR - System error in TransactionManager.begin_transaction: Connection failed
2025-05-27 16:35:02,731 - transaction - INFO - Transaction 35af7ec7-e3c2-495e-a2a4-63f85411a6f0 started with participants: ['participant_1', 'participant_2']
2025-05-27 16:35:02,733 - transaction - INFO - Transaction 35af7ec7-e3c2-495e-a2a4-63f85411a6f0 prepare phase - Participant participant_1: SUCCESS
2025-05-27 16:35:02,734 - transaction - INFO - Transaction 35af7ec7-e3c2-495e-a2a4-63f85411a6f0 prepare phase - Participant participant_2: SUCCESS
2025-05-27 16:35:02,734 - transaction - INFO - Transaction 35af7ec7-e3c2-495e-a2a4-63f85411a6f0 prepared successfully
2025-05-27 16:35:02,736 - transaction - INFO - Transaction 894f5635-7717-434d-9f1b-f8638b2609de started with participants: ['participant_1', 'participant_2']
2025-05-27 16:35:02,737 - transaction - INFO - Transaction 894f5635-7717-434d-9f1b-f8638b2609de prepare phase - Participant participant_1: FAILED
2025-05-27 16:35:02,738 - system - ERROR - System error in TransactionManager.prepare: Prepare failed for participant_1: Prepare failed
2025-05-27 16:35:02,749 - transaction - INFO - Transaction 894f5635-7717-434d-9f1b-f8638b2609de ROLLBACK
2025-05-27 16:35:02,751 - transaction - INFO - Transaction 894f5635-7717-434d-9f1b-f8638b2609de rolled back successfully
2025-05-27 16:35:02,753 - transaction - INFO - Transaction 055206f6-4c0e-48e3-8047-b8129864c40b started with participants: ['participant_1', 'participant_2']
2025-05-27 16:35:02,754 - transaction - INFO - Transaction 055206f6-4c0e-48e3-8047-b8129864c40b COMMITTED
2025-05-27 16:35:02,754 - transaction - INFO - Transaction 055206f6-4c0e-48e3-8047-b8129864c40b committed successfully
2025-05-27 16:35:02,755 - transaction - INFO - Transaction 858aeb41-2ad3-4598-8265-b77963b91f6e started with participants: ['participant_1', 'participant_2']
2025-05-27 16:35:02,757 - transaction - INFO - Transaction 858aeb41-2ad3-4598-8265-b77963b91f6e ROLLBACK
2025-05-27 16:35:02,757 - transaction - INFO - Transaction 858aeb41-2ad3-4598-8265-b77963b91f6e rolled back successfully
2025-05-27 16:35:02,759 - transaction - INFO - Transaction e8a3f593-c865-4d71-8ce0-47434a2e8ecd started with participants: ['participant_1', 'participant_2']
2025-05-27 16:35:02,761 - transaction - INFO - Transaction 97a537b8-1242-467e-8900-25a4741e5bb2 started with participants: ['participant_1', 'participant_2']
2025-05-27 16:35:02,763 - database - INFO - Database connection Connection pool created for node db1 - localhost:3306
2025-05-27 16:35:02,764 - database - INFO - Database connection Connection pool created for node db2 - localhost:3307
2025-05-27 16:35:02,764 - database - INFO - Database manager initialized with nodes: db1, db2
2025-05-27 16:35:02,766 - database - INFO - Database connection Connection pool created for node db1 - localhost:3306
2025-05-27 16:35:02,767 - database - INFO - Database connection Connection pool created for node db2 - localhost:3307
2025-05-27 16:35:02,768 - database - INFO - Database manager initialized with nodes: db1, db2
2025-05-27 16:35:02,769 - database - INFO - Database connection Connection pool created for node test - localhost:3306
2025-05-27 16:35:02,771 - database - INFO - Database connection Connection pool created for node db1 - localhost:3306
2025-05-27 16:35:02,772 - database - INFO - Database connection Connection pool created for node db2 - localhost:3307
2025-05-27 16:35:02,773 - database - INFO - Database manager initialized with nodes: db1, db2
2025-05-27 16:35:02,774 - database - INFO - Database connection Connection pool created for node test - localhost:3306
2025-05-27 16:35:02,777 - system - INFO - BankingService: Account 1001 created with balance 1000.0
2025-05-27 16:35:02,780 - system - ERROR - System error in BankingService.create_account: Database error
2025-05-27 16:35:02,788 - transaction - INFO - Transaction a6022967-0169-490e-bf63-c9ff42f84e93 started with participants: ['participant_1', 'participant_2', 'participant_3', 'participant_4', 'participant_5', 'participant_6', 'participant_7', 'participant_8', 'participant_9', 'participant_10']
2025-05-27 16:35:02,789 - transaction - INFO - Transaction a6022967-0169-490e-bf63-c9ff42f84e93 started successfully
2025-05-27 16:35:02,791 - transaction - INFO - Transaction 8cedb74b-0bca-49aa-92fb-b84606675f0a started with participants: ['participant_1', 'participant_2']
2025-05-27 16:35:02,792 - transaction - INFO - Transaction e5b373b1-1fd7-455a-9181-f08b6f12f4e8 started with participants: ['participant_1', 'participant_2']
2025-05-27 16:35:02,792 - transaction - INFO - Transaction 619a97b7-a2f0-441b-8aff-6fdd2b1ee87f started with participants: ['participant_1', 'participant_2']
2025-05-27 16:35:02,792 - transaction - INFO - Transaction 7999f19c-db63-4a98-8672-8a90f9a65996 started with participants: ['participant_1', 'participant_2']
2025-05-27 16:35:02,793 - transaction - INFO - Transaction 82582a8e-46ab-49df-a058-7bcc6fb9ec12 started with participants: ['participant_1', 'participant_2']
2025-05-27 16:35:02,794 - transaction - INFO - Transaction 8cedb74b-0bca-49aa-92fb-b84606675f0a started successfully
2025-05-27 16:35:02,794 - transaction - INFO - Transaction e5b373b1-1fd7-455a-9181-f08b6f12f4e8 started successfully
2025-05-27 16:35:02,795 - transaction - INFO - Transaction 619a97b7-a2f0-441b-8aff-6fdd2b1ee87f started successfully
2025-05-27 16:35:02,796 - transaction - INFO - Transaction 7999f19c-db63-4a98-8672-8a90f9a65996 started successfully
2025-05-27 16:35:02,796 - transaction - INFO - Transaction 82582a8e-46ab-49df-a058-7bcc6fb9ec12 started successfully
2025-05-27 16:44:23,859 - system - INFO - DatabaseInit: Waiting for database localhost:3306 (attempt 1/30)
2025-05-27 16:44:25,867 - system - INFO - DatabaseInit: Waiting for database localhost:3306 (attempt 2/30)
2025-05-27 16:44:27,876 - system - INFO - DatabaseInit: Waiting for database localhost:3306 (attempt 3/30)
2025-05-27 16:44:29,884 - system - INFO - DatabaseInit: Waiting for database localhost:3306 (attempt 4/30)
2025-05-27 16:44:31,911 - system - INFO - DatabaseInit: Waiting for database localhost:3306 (attempt 5/30)
2025-05-27 16:44:33,936 - system - INFO - DatabaseInit: Waiting for database localhost:3306 (attempt 6/30)
2025-05-27 16:44:35,944 - system - INFO - DatabaseInit: Waiting for database localhost:3306 (attempt 7/30)
2025-05-27 16:44:37,972 - system - INFO - DatabaseInit: Waiting for database localhost:3306 (attempt 8/30)
2025-05-27 16:44:39,990 - system - INFO - DatabaseInit: Waiting for database localhost:3306 (attempt 9/30)
2025-05-27 16:44:41,998 - system - INFO - DatabaseInit: Waiting for database localhost:3306 (attempt 10/30)
2025-05-27 16:44:44,006 - system - INFO - DatabaseInit: Waiting for database localhost:3306 (attempt 11/30)
2025-05-27 16:44:46,024 - system - INFO - DatabaseInit: Waiting for database localhost:3306 (attempt 12/30)
2025-05-27 16:44:48,034 - system - INFO - DatabaseInit: Waiting for database localhost:3306 (attempt 13/30)
2025-05-27 16:44:50,063 - system - INFO - DatabaseInit: Waiting for database localhost:3306 (attempt 14/30)
2025-05-27 16:44:52,068 - system - INFO - DatabaseInit: Waiting for database localhost:3306 (attempt 15/30)
2025-05-27 16:44:54,099 - system - INFO - DatabaseInit: Waiting for database localhost:3306 (attempt 16/30)
2025-05-27 16:44:56,118 - system - INFO - DatabaseInit: Waiting for database localhost:3306 (attempt 17/30)
2025-05-27 16:44:58,126 - system - INFO - DatabaseInit: Waiting for database localhost:3306 (attempt 18/30)
2025-05-27 16:45:00,133 - system - INFO - DatabaseInit: Waiting for database localhost:3306 (attempt 19/30)
2025-05-27 16:45:02,165 - system - INFO - DatabaseInit: Waiting for database localhost:3306 (attempt 20/30)
2025-05-27 16:45:04,174 - system - INFO - DatabaseInit: Waiting for database localhost:3306 (attempt 21/30)
2025-05-27 16:45:06,203 - system - INFO - DatabaseInit: Waiting for database localhost:3306 (attempt 22/30)
2025-05-27 16:45:08,211 - system - INFO - DatabaseInit: Waiting for database localhost:3306 (attempt 23/30)
2025-05-27 16:45:10,218 - system - INFO - DatabaseInit: Waiting for database localhost:3306 (attempt 24/30)
2025-05-27 16:45:12,226 - system - INFO - DatabaseInit: Waiting for database localhost:3306 (attempt 25/30)
2025-05-27 16:45:14,235 - system - INFO - DatabaseInit: Waiting for database localhost:3306 (attempt 26/30)
2025-05-27 16:45:16,243 - system - INFO - DatabaseInit: Waiting for database localhost:3306 (attempt 27/30)
2025-05-27 16:45:18,253 - system - INFO - DatabaseInit: Waiting for database localhost:3306 (attempt 28/30)
2025-05-27 16:45:20,262 - system - INFO - DatabaseInit: Waiting for database localhost:3306 (attempt 29/30)
2025-05-27 16:45:22,271 - system - ERROR - System error in DatabaseInit: Failed to connect to database localhost:3306 after 30 attempts: 1045 (28000): Access denied for user 'root'@'localhost' (using password: YES)
2025-05-27 16:45:26,344 - system - INFO - DatabaseInit: Waiting for database localhost:3307 (attempt 1/30)
2025-05-27 16:45:32,427 - system - INFO - DatabaseInit: Waiting for database localhost:3307 (attempt 2/30)
2025-05-27 16:45:38,539 - system - INFO - DatabaseInit: Waiting for database localhost:3307 (attempt 3/30)
2025-05-27 16:45:44,636 - system - INFO - DatabaseInit: Waiting for database localhost:3307 (attempt 4/30)
2025-05-27 16:45:50,718 - system - INFO - DatabaseInit: Waiting for database localhost:3307 (attempt 5/30)
2025-05-27 16:45:56,825 - system - INFO - DatabaseInit: Waiting for database localhost:3307 (attempt 6/30)
2025-05-27 16:46:02,917 - system - INFO - DatabaseInit: Waiting for database localhost:3307 (attempt 7/30)
2025-05-27 16:46:09,020 - system - INFO - DatabaseInit: Waiting for database localhost:3307 (attempt 8/30)
2025-05-27 16:46:15,094 - system - INFO - DatabaseInit: Waiting for database localhost:3307 (attempt 9/30)
2025-05-27 16:46:21,192 - system - INFO - DatabaseInit: Waiting for database localhost:3307 (attempt 10/30)
2025-05-27 16:46:27,285 - system - INFO - DatabaseInit: Waiting for database localhost:3307 (attempt 11/30)
2025-05-27 16:46:33,378 - system - INFO - DatabaseInit: Waiting for database localhost:3307 (attempt 12/30)
2025-05-27 16:46:39,488 - system - INFO - DatabaseInit: Waiting for database localhost:3307 (attempt 13/30)
2025-05-27 16:46:45,577 - system - INFO - DatabaseInit: Waiting for database localhost:3307 (attempt 14/30)
2025-05-27 16:46:51,669 - system - INFO - DatabaseInit: Waiting for database localhost:3307 (attempt 15/30)
2025-05-27 16:46:57,766 - system - INFO - DatabaseInit: Waiting for database localhost:3307 (attempt 16/30)
2025-05-27 16:47:03,863 - system - INFO - DatabaseInit: Waiting for database localhost:3307 (attempt 17/30)
2025-05-27 16:47:09,933 - system - INFO - DatabaseInit: Waiting for database localhost:3307 (attempt 18/30)
2025-05-27 16:47:16,044 - system - INFO - DatabaseInit: Waiting for database localhost:3307 (attempt 19/30)
2025-05-27 16:47:22,153 - system - INFO - DatabaseInit: Waiting for database localhost:3307 (attempt 20/30)
2025-05-27 16:47:28,228 - system - INFO - DatabaseInit: Waiting for database localhost:3307 (attempt 21/30)
2025-05-27 16:47:34,318 - system - INFO - DatabaseInit: Waiting for database localhost:3307 (attempt 22/30)
2025-05-27 16:47:40,405 - system - INFO - DatabaseInit: Waiting for database localhost:3307 (attempt 23/30)
2025-05-27 16:47:46,523 - system - INFO - DatabaseInit: Waiting for database localhost:3307 (attempt 24/30)
2025-05-27 16:47:52,623 - system - INFO - DatabaseInit: Waiting for database localhost:3307 (attempt 25/30)
2025-05-27 16:47:58,726 - system - INFO - DatabaseInit: Waiting for database localhost:3307 (attempt 26/30)
2025-05-27 16:48:04,800 - system - INFO - DatabaseInit: Waiting for database localhost:3307 (attempt 27/30)
2025-05-27 16:48:10,892 - system - INFO - DatabaseInit: Waiting for database localhost:3307 (attempt 28/30)
2025-05-27 16:48:16,963 - system - INFO - DatabaseInit: Waiting for database localhost:3307 (attempt 29/30)
2025-05-27 16:48:23,061 - system - ERROR - System error in DatabaseInit: Failed to connect to database localhost:3307 after 30 attempts: 2003 (HY000): Can't connect to MySQL server on 'localhost:3307' (10061)
2025-05-27 17:52:54,373 - system - INFO - DatabaseInit: Database localhost:3316 is ready
2025-05-27 17:52:54,569 - system - INFO - DatabaseInit: Database db1 setup completed
2025-05-27 17:52:54,600 - system - INFO - DatabaseInit: Database localhost:3317 is ready
2025-05-27 17:52:54,916 - system - INFO - DatabaseInit: Database db2 setup completed
2025-05-27 17:52:54,980 - system - INFO - DatabaseInit: Database verification completed - Accounts: 5, Products: 5
2025-05-27 18:08:42,369 - system - INFO - DatabaseInit: Database localhost:3316 is ready
2025-05-27 18:08:42,410 - system - INFO - DatabaseInit: Database db1 setup completed
2025-05-27 18:08:42,435 - system - INFO - DatabaseInit: Database localhost:3317 is ready
2025-05-27 18:08:42,480 - system - INFO - DatabaseInit: Database db2 setup completed
2025-05-27 18:08:42,510 - system - INFO - DatabaseInit: Database verification completed - Accounts: 5, Products: 5
2025-05-27 18:08:47,012 - database - INFO - Database connection Connection pool created for node db1 - localhost:3316
2025-05-27 18:08:47,134 - database - INFO - Database connection Connection pool created for node db2 - localhost:3317
2025-05-27 18:08:47,135 - database - INFO - Database manager initialized with nodes: db1, db2
2025-05-27 18:08:47,217 - transaction - INFO - Transaction f36cb98f-81e6-4e4e-905c-37af967106b7 started with participants: ['participant_1', 'participant_2']
2025-05-27 18:08:47,274 - transaction - INFO - Transaction f55ffebc-3ed2-4526-ae36-90c26d8418b9 started with participants: ['participant_1', 'participant_2']
2025-05-27 18:08:47,290 - transaction - INFO - Transaction f55ffebc-3ed2-4526-ae36-90c26d8418b9 started successfully
2025-05-27 18:08:47,292 - transaction - INFO - Transaction 77681ec2-7ede-4c8c-8707-85472f9b1d86 started with participants: ['participant_1', 'participant_2']
2025-05-27 18:08:47,293 - system - ERROR - System error in TransactionManager.begin_transaction: Connection failed
2025-05-27 18:08:47,307 - transaction - INFO - Transaction c6449625-ac9e-406a-921b-670e05978d11 started with participants: ['participant_1', 'participant_2']
2025-05-27 18:08:47,308 - transaction - INFO - Transaction c6449625-ac9e-406a-921b-670e05978d11 prepare phase - Participant participant_1: SUCCESS
2025-05-27 18:08:47,309 - transaction - INFO - Transaction c6449625-ac9e-406a-921b-670e05978d11 prepare phase - Participant participant_2: SUCCESS
2025-05-27 18:08:47,310 - transaction - INFO - Transaction c6449625-ac9e-406a-921b-670e05978d11 prepared successfully
2025-05-27 18:08:47,311 - transaction - INFO - Transaction 9481e2a4-4480-4820-861f-7cec705b8acb started with participants: ['participant_1', 'participant_2']
2025-05-27 18:08:47,312 - transaction - INFO - Transaction 9481e2a4-4480-4820-861f-7cec705b8acb prepare phase - Participant participant_1: FAILED
2025-05-27 18:08:47,313 - system - ERROR - System error in TransactionManager.prepare: Prepare failed for participant_1: Prepare failed
2025-05-27 18:08:47,314 - transaction - INFO - Transaction 9481e2a4-4480-4820-861f-7cec705b8acb ROLLBACK
2025-05-27 18:08:47,315 - transaction - INFO - Transaction 9481e2a4-4480-4820-861f-7cec705b8acb rolled back successfully
2025-05-27 18:08:47,316 - transaction - INFO - Transaction a65e4aa8-c769-4134-b7d5-ccde20293a59 started with participants: ['participant_1', 'participant_2']
2025-05-27 18:08:47,317 - transaction - INFO - Transaction a65e4aa8-c769-4134-b7d5-ccde20293a59 COMMITTED
2025-05-27 18:08:47,317 - transaction - INFO - Transaction a65e4aa8-c769-4134-b7d5-ccde20293a59 committed successfully
2025-05-27 18:08:47,319 - transaction - INFO - Transaction 209b0ebc-8eaf-40bd-a6a7-4664665baddb started with participants: ['participant_1', 'participant_2']
2025-05-27 18:08:47,320 - transaction - INFO - Transaction 209b0ebc-8eaf-40bd-a6a7-4664665baddb ROLLBACK
2025-05-27 18:08:47,320 - transaction - INFO - Transaction 209b0ebc-8eaf-40bd-a6a7-4664665baddb rolled back successfully
2025-05-27 18:08:47,321 - transaction - INFO - Transaction 45d6185b-87d8-442b-8888-9c43e2a389f8 started with participants: ['participant_1', 'participant_2']
2025-05-27 18:08:47,323 - transaction - INFO - Transaction 5109b5c0-5651-4a8d-855a-4baa05178f18 started with participants: ['participant_1', 'participant_2']
2025-05-27 18:08:47,325 - database - INFO - Database connection Connection pool created for node db1 - localhost:3306
2025-05-27 18:08:47,326 - database - INFO - Database connection Connection pool created for node db2 - localhost:3307
2025-05-27 18:08:47,327 - database - INFO - Database manager initialized with nodes: db1, db2
2025-05-27 18:08:47,329 - database - INFO - Database connection Connection pool created for node db1 - localhost:3306
2025-05-27 18:08:47,329 - database - INFO - Database connection Connection pool created for node db2 - localhost:3307
2025-05-27 18:08:47,330 - database - INFO - Database manager initialized with nodes: db1, db2
2025-05-27 18:08:47,331 - database - INFO - Database connection Connection pool created for node test - localhost:3306
2025-05-27 18:08:47,334 - database - INFO - Database connection Connection pool created for node db1 - localhost:3306
2025-05-27 18:08:47,334 - database - INFO - Database connection Connection pool created for node db2 - localhost:3307
2025-05-27 18:08:47,335 - database - INFO - Database manager initialized with nodes: db1, db2
2025-05-27 18:08:47,336 - database - INFO - Database connection Connection pool created for node test - localhost:3306
2025-05-27 18:08:47,338 - system - INFO - BankingService: Account 1001 created with balance 1000.0
2025-05-27 18:08:47,340 - system - ERROR - System error in BankingService.create_account: Database error
2025-05-27 18:08:47,346 - transaction - INFO - Transaction 82e0bb2d-df65-48a7-8080-57b4544aaf8b started with participants: ['participant_1', 'participant_2', 'participant_3', 'participant_4', 'participant_5', 'participant_6', 'participant_7', 'participant_8', 'participant_9', 'participant_10']
2025-05-27 18:08:47,347 - transaction - INFO - Transaction 82e0bb2d-df65-48a7-8080-57b4544aaf8b started successfully
2025-05-27 18:08:47,349 - transaction - INFO - Transaction becf68a9-2540-4441-8840-8c24bb14ebda started with participants: ['participant_1', 'participant_2']
2025-05-27 18:08:47,350 - transaction - INFO - Transaction 00bc7e0c-58e6-4a1b-99da-6846a881b508 started with participants: ['participant_1', 'participant_2']
2025-05-27 18:08:47,350 - transaction - INFO - Transaction 6d921b56-5f06-4990-ad88-c6a023018eee started with participants: ['participant_1', 'participant_2']
2025-05-27 18:08:47,350 - transaction - INFO - Transaction 7b078c53-eacd-43b1-b049-33d0c7fb5291 started with participants: ['participant_1', 'participant_2']
2025-05-27 18:08:47,351 - transaction - INFO - Transaction e34f8053-3a7e-4963-9b74-508b7082cf5a started with participants: ['participant_1', 'participant_2']
2025-05-27 18:08:47,367 - transaction - INFO - Transaction becf68a9-2540-4441-8840-8c24bb14ebda started successfully
2025-05-27 18:08:47,368 - transaction - INFO - Transaction 00bc7e0c-58e6-4a1b-99da-6846a881b508 started successfully
2025-05-27 18:08:47,368 - transaction - INFO - Transaction 6d921b56-5f06-4990-ad88-c6a023018eee started successfully
2025-05-27 18:08:47,370 - transaction - INFO - Transaction 7b078c53-eacd-43b1-b049-33d0c7fb5291 started successfully
2025-05-27 18:08:47,371 - transaction - INFO - Transaction e34f8053-3a7e-4963-9b74-508b7082cf5a started successfully
2025-05-27 18:08:54,817 - database - INFO - Database connection Connection pool created for node db1 - localhost:3316
2025-05-27 18:08:54,941 - database - INFO - Database connection Connection pool created for node db2 - localhost:3317
2025-05-27 18:08:54,942 - database - INFO - Database manager initialized with nodes: db1, db2
2025-05-27 18:08:54,994 - transaction - INFO - Transaction 36b4da90-4569-496f-96b5-1cd9069ed857 started with participants: ['participant_1', 'participant_2']
2025-05-27 18:08:55,000 - transaction - INFO - Transaction 67529274-7e4c-4aae-88ec-b3e248b21205 started with participants: ['participant_1', 'participant_2']
2025-05-27 18:08:55,010 - transaction - INFO - Transaction 67529274-7e4c-4aae-88ec-b3e248b21205 started successfully
2025-05-27 18:08:55,012 - transaction - INFO - Transaction 7cd9ac70-1e75-4bef-82c0-c67ed381e58a started with participants: ['participant_1', 'participant_2']
2025-05-27 18:08:55,013 - system - ERROR - System error in TransactionManager.begin_transaction: Connection failed
2025-05-27 18:08:55,016 - transaction - INFO - Transaction 60a4643c-2941-4aea-9b9a-2e07562189db started with participants: ['participant_1', 'participant_2']
2025-05-27 18:08:55,017 - transaction - INFO - Transaction 60a4643c-2941-4aea-9b9a-2e07562189db prepare phase - Participant participant_1: SUCCESS
2025-05-27 18:08:55,018 - transaction - INFO - Transaction 60a4643c-2941-4aea-9b9a-2e07562189db prepare phase - Participant participant_2: SUCCESS
2025-05-27 18:08:55,018 - transaction - INFO - Transaction 60a4643c-2941-4aea-9b9a-2e07562189db prepared successfully
2025-05-27 18:08:55,019 - transaction - INFO - Transaction dd7edb5e-d238-44a2-a8a9-d8f3e180424d started with participants: ['participant_1', 'participant_2']
2025-05-27 18:08:55,020 - transaction - INFO - Transaction dd7edb5e-d238-44a2-a8a9-d8f3e180424d prepare phase - Participant participant_1: FAILED
2025-05-27 18:08:55,021 - system - ERROR - System error in TransactionManager.prepare: Prepare failed for participant_1: Prepare failed
2025-05-27 18:08:55,022 - transaction - INFO - Transaction dd7edb5e-d238-44a2-a8a9-d8f3e180424d ROLLBACK
2025-05-27 18:08:55,022 - transaction - INFO - Transaction dd7edb5e-d238-44a2-a8a9-d8f3e180424d rolled back successfully
2025-05-27 18:08:55,024 - transaction - INFO - Transaction 82c7e4f6-5ad4-40cb-9ab0-ccdb9b38855f started with participants: ['participant_1', 'participant_2']
2025-05-27 18:08:55,025 - transaction - INFO - Transaction 82c7e4f6-5ad4-40cb-9ab0-ccdb9b38855f COMMITTED
2025-05-27 18:08:55,026 - transaction - INFO - Transaction 82c7e4f6-5ad4-40cb-9ab0-ccdb9b38855f committed successfully
2025-05-27 18:08:55,027 - transaction - INFO - Transaction 3e1adfee-ad1c-4a62-a290-e6632cc2d568 started with participants: ['participant_1', 'participant_2']
2025-05-27 18:08:55,028 - transaction - INFO - Transaction 3e1adfee-ad1c-4a62-a290-e6632cc2d568 ROLLBACK
2025-05-27 18:08:55,029 - transaction - INFO - Transaction 3e1adfee-ad1c-4a62-a290-e6632cc2d568 rolled back successfully
2025-05-27 18:08:55,030 - transaction - INFO - Transaction 0020fd99-fa02-4981-8685-f7deeccc78f1 started with participants: ['participant_1', 'participant_2']
2025-05-27 18:08:55,032 - transaction - INFO - Transaction c78f6532-8f75-4f32-b366-d1651b4e5290 started with participants: ['participant_1', 'participant_2']
2025-05-27 18:08:55,035 - database - INFO - Database connection Connection pool created for node db1 - localhost:3306
2025-05-27 18:08:55,036 - database - INFO - Database connection Connection pool created for node db2 - localhost:3307
2025-05-27 18:08:55,037 - database - INFO - Database manager initialized with nodes: db1, db2
2025-05-27 18:08:55,039 - database - INFO - Database connection Connection pool created for node db1 - localhost:3306
2025-05-27 18:08:55,039 - database - INFO - Database connection Connection pool created for node db2 - localhost:3307
2025-05-27 18:08:55,040 - database - INFO - Database manager initialized with nodes: db1, db2
2025-05-27 18:08:55,041 - database - INFO - Database connection Connection pool created for node test - localhost:3306
2025-05-27 18:08:55,043 - database - INFO - Database connection Connection pool created for node db1 - localhost:3306
2025-05-27 18:08:55,043 - database - INFO - Database connection Connection pool created for node db2 - localhost:3307
2025-05-27 18:08:55,044 - database - INFO - Database manager initialized with nodes: db1, db2
2025-05-27 18:08:55,045 - database - INFO - Database connection Connection pool created for node test - localhost:3306
2025-05-27 18:08:55,048 - system - INFO - BankingService: Account 1001 created with balance 1000.0
2025-05-27 18:08:55,050 - system - ERROR - System error in BankingService.create_account: Database error
2025-05-27 18:08:55,056 - transaction - INFO - Transaction e97a877b-1033-48da-a866-5e16cf4f2123 started with participants: ['participant_1', 'participant_2', 'participant_3', 'participant_4', 'participant_5', 'participant_6', 'participant_7', 'participant_8', 'participant_9', 'participant_10']
2025-05-27 18:08:55,058 - transaction - INFO - Transaction e97a877b-1033-48da-a866-5e16cf4f2123 started successfully
2025-05-27 18:08:55,059 - transaction - INFO - Transaction 5fbd3426-6ef4-4ae7-87df-6eddc879aad9 started with participants: ['participant_1', 'participant_2']
2025-05-27 18:08:55,059 - transaction - INFO - Transaction 71c5acbc-c9f8-478c-af57-c84cee5a54e6 started with participants: ['participant_1', 'participant_2']
2025-05-27 18:08:55,059 - transaction - INFO - Transaction 89f50699-8680-4a05-b2f8-254ab1635aa3 started with participants: ['participant_1', 'participant_2']
2025-05-27 18:08:55,060 - transaction - INFO - Transaction ae28125a-a328-415d-8ec2-d702d5547aaa started with participants: ['participant_1', 'participant_2']
2025-05-27 18:08:55,060 - transaction - INFO - Transaction ecf0445e-f2fb-45a8-945e-8714ff4401c2 started with participants: ['participant_1', 'participant_2']
2025-05-27 18:08:55,061 - transaction - INFO - Transaction 5fbd3426-6ef4-4ae7-87df-6eddc879aad9 started successfully
2025-05-27 18:08:55,062 - transaction - INFO - Transaction 71c5acbc-c9f8-478c-af57-c84cee5a54e6 started successfully
2025-05-27 18:08:55,063 - transaction - INFO - Transaction 89f50699-8680-4a05-b2f8-254ab1635aa3 started successfully
2025-05-27 18:08:55,064 - transaction - INFO - Transaction ae28125a-a328-415d-8ec2-d702d5547aaa started successfully
2025-05-27 18:08:55,065 - transaction - INFO - Transaction ecf0445e-f2fb-45a8-945e-8714ff4401c2 started successfully
2025-05-27 18:19:17,845 - database - INFO - Database connection Connection pool created for node db1 - localhost:3316
2025-05-27 18:19:17,936 - database - INFO - Database connection Connection pool created for node db2 - localhost:3317
2025-05-27 18:19:17,937 - database - INFO - Database manager initialized with nodes: db1, db2
2025-05-27 18:19:17,949 - system - INFO - BankingService: Account 9999 created with balance 100.0
2025-05-27 18:19:17,954 - database - INFO - Database operation SUCCESS - SELECT on db1.query
2025-05-27 18:23:12,891 - system - INFO - DatabaseInit: Database localhost:3316 is ready
2025-05-27 18:23:12,932 - system - INFO - DatabaseInit: Database db1 setup completed
2025-05-27 18:23:12,960 - system - INFO - DatabaseInit: Database localhost:3317 is ready
2025-05-27 18:23:12,994 - system - INFO - DatabaseInit: Database db2 setup completed
2025-05-27 18:23:13,054 - system - INFO - DatabaseInit: Database verification completed - Accounts: 6, Products: 5
2025-05-27 18:23:16,262 - database - INFO - Database connection Connection pool created for node db1 - localhost:3316
2025-05-27 18:23:16,323 - database - INFO - Database connection Connection pool created for node db2 - localhost:3317
2025-05-27 18:23:16,324 - database - INFO - Database manager initialized with nodes: db1, db2
2025-05-27 18:23:16,375 - transaction - INFO - Transaction 5f90eb97-76f8-4ace-8a78-7b59d3fba7a5 started with participants: ['participant_1', 'participant_2']
2025-05-27 18:23:16,382 - transaction - INFO - Transaction 5bdacf43-7c51-4bed-bfc7-720b08c7139c started with participants: ['participant_1', 'participant_2']
2025-05-27 18:23:16,392 - transaction - INFO - Transaction 5bdacf43-7c51-4bed-bfc7-720b08c7139c started successfully
2025-05-27 18:23:16,394 - transaction - INFO - Transaction e05bacdf-51e2-48f0-b5fd-0bfd27f8ad08 started with participants: ['participant_1', 'participant_2']
2025-05-27 18:23:16,394 - system - ERROR - System error in TransactionManager.begin_transaction: Connection failed
2025-05-27 18:23:16,397 - transaction - INFO - Transaction 925dc19f-bdd5-4f1b-b4eb-0bfbed2ea6d9 started with participants: ['participant_1', 'participant_2']
2025-05-27 18:23:16,398 - transaction - INFO - Transaction 925dc19f-bdd5-4f1b-b4eb-0bfbed2ea6d9 prepare phase - Participant participant_1: SUCCESS
2025-05-27 18:23:16,398 - transaction - INFO - Transaction 925dc19f-bdd5-4f1b-b4eb-0bfbed2ea6d9 prepare phase - Participant participant_2: SUCCESS
2025-05-27 18:23:16,399 - transaction - INFO - Transaction 925dc19f-bdd5-4f1b-b4eb-0bfbed2ea6d9 prepared successfully
2025-05-27 18:23:16,400 - transaction - INFO - Transaction 5b80f9ff-1850-41b5-9aa6-0604aa97e7ab started with participants: ['participant_1', 'participant_2']
2025-05-27 18:23:16,401 - transaction - INFO - Transaction 5b80f9ff-1850-41b5-9aa6-0604aa97e7ab prepare phase - Participant participant_1: FAILED
2025-05-27 18:23:16,402 - system - ERROR - System error in TransactionManager.prepare: Prepare failed for participant_1: Prepare failed
2025-05-27 18:23:16,403 - transaction - INFO - Transaction 5b80f9ff-1850-41b5-9aa6-0604aa97e7ab ROLLBACK
2025-05-27 18:23:16,403 - transaction - INFO - Transaction 5b80f9ff-1850-41b5-9aa6-0604aa97e7ab rolled back successfully
2025-05-27 18:23:16,405 - transaction - INFO - Transaction 66680319-34fc-4fc7-9d3c-ff09a8c9fefd started with participants: ['participant_1', 'participant_2']
2025-05-27 18:23:16,406 - transaction - INFO - Transaction 66680319-34fc-4fc7-9d3c-ff09a8c9fefd COMMITTED
2025-05-27 18:23:16,406 - transaction - INFO - Transaction 66680319-34fc-4fc7-9d3c-ff09a8c9fefd committed successfully
2025-05-27 18:23:16,408 - transaction - INFO - Transaction 53db45da-1412-406c-93b6-bdc1001573ae started with participants: ['participant_1', 'participant_2']
2025-05-27 18:23:16,409 - transaction - INFO - Transaction 53db45da-1412-406c-93b6-bdc1001573ae ROLLBACK
2025-05-27 18:23:16,409 - transaction - INFO - Transaction 53db45da-1412-406c-93b6-bdc1001573ae rolled back successfully
2025-05-27 18:23:16,410 - transaction - INFO - Transaction 0564af89-d4b7-48db-9312-efaf9d24667f started with participants: ['participant_1', 'participant_2']
2025-05-27 18:23:16,413 - transaction - INFO - Transaction 481c790f-f5bf-41eb-b692-1eb7114c2cc7 started with participants: ['participant_1', 'participant_2']
2025-05-27 18:23:16,416 - database - INFO - Database connection Connection pool created for node db1 - localhost:3306
2025-05-27 18:23:16,416 - database - INFO - Database connection Connection pool created for node db2 - localhost:3307
2025-05-27 18:23:16,417 - database - INFO - Database manager initialized with nodes: db1, db2
2025-05-27 18:23:16,419 - database - INFO - Database connection Connection pool created for node db1 - localhost:3306
2025-05-27 18:23:16,420 - database - INFO - Database connection Connection pool created for node db2 - localhost:3307
2025-05-27 18:23:16,420 - database - INFO - Database manager initialized with nodes: db1, db2
2025-05-27 18:23:16,421 - database - INFO - Database connection Connection pool created for node test - localhost:3306
2025-05-27 18:23:16,423 - database - INFO - Database connection Connection pool created for node db1 - localhost:3306
2025-05-27 18:23:16,423 - database - INFO - Database connection Connection pool created for node db2 - localhost:3307
2025-05-27 18:23:16,424 - database - INFO - Database manager initialized with nodes: db1, db2
2025-05-27 18:23:16,425 - database - INFO - Database connection Connection pool created for node test - localhost:3306
2025-05-27 18:23:16,427 - system - INFO - BankingService: Account 1001 created with balance 1000.0
2025-05-27 18:23:16,429 - system - ERROR - System error in BankingService.create_account: Database error: Database error
2025-05-27 18:23:16,435 - transaction - INFO - Transaction be6ab409-7cc7-45a1-b467-afa238b50f74 started with participants: ['participant_1', 'participant_2', 'participant_3', 'participant_4', 'participant_5', 'participant_6', 'participant_7', 'participant_8', 'participant_9', 'participant_10']
2025-05-27 18:23:16,436 - transaction - INFO - Transaction be6ab409-7cc7-45a1-b467-afa238b50f74 started successfully
2025-05-27 18:23:16,438 - transaction - INFO - Transaction c4534fd8-799a-4b7e-ae64-81f0775f32c6 started with participants: ['participant_1', 'participant_2']
2025-05-27 18:23:16,438 - transaction - INFO - Transaction acaed0d0-088d-4fbe-86d7-95b13ffcbea2 started with participants: ['participant_1', 'participant_2']
2025-05-27 18:23:16,439 - transaction - INFO - Transaction ********-f8f0-4c60-ada9-55249d3ca2e4 started with participants: ['participant_1', 'participant_2']
2025-05-27 18:23:16,439 - transaction - INFO - Transaction 321f5109-6678-4439-83ba-58f8afb959ec started with participants: ['participant_1', 'participant_2']
2025-05-27 18:23:16,439 - transaction - INFO - Transaction c45aa069-7d34-49f9-9b30-ca19907e53ba started with participants: ['participant_1', 'participant_2']
2025-05-27 18:23:16,441 - transaction - INFO - Transaction c4534fd8-799a-4b7e-ae64-81f0775f32c6 started successfully
2025-05-27 18:23:16,441 - transaction - INFO - Transaction acaed0d0-088d-4fbe-86d7-95b13ffcbea2 started successfully
2025-05-27 18:23:16,442 - transaction - INFO - Transaction ********-f8f0-4c60-ada9-55249d3ca2e4 started successfully
2025-05-27 18:23:16,444 - transaction - INFO - Transaction 321f5109-6678-4439-83ba-58f8afb959ec started successfully
2025-05-27 18:23:16,445 - transaction - INFO - Transaction c45aa069-7d34-49f9-9b30-ca19907e53ba started successfully
2025-05-27 18:32:31,383 - database - INFO - Database connection Connection pool created for node db1 - localhost:3316
2025-05-27 18:32:31,475 - database - INFO - Database connection Connection pool created for node db2 - localhost:3317
2025-05-27 18:32:31,476 - database - INFO - Database manager initialized with nodes: db1, db2
2025-05-27 18:32:31,498 - system - INFO - BankingService: Account 8888 created with balance 200.0
2025-05-27 18:32:31,503 - database - INFO - Database operation SUCCESS - SELECT on db1.query
2025-05-27 18:47:53,951 - transaction - INFO - Transaction b04a7c04-a8cd-415f-92a6-d3e5adb0add7 started with participants: ['participant_1', 'participant_2']
2025-05-27 18:47:53,965 - transaction - INFO - Transaction bc914aef-b26a-4c5d-af59-97f5e4e63a6f started with participants: ['participant_1', 'participant_2']
2025-05-27 18:47:53,966 - transaction - INFO - Transaction bc914aef-b26a-4c5d-af59-97f5e4e63a6f started successfully
2025-05-27 18:47:53,968 - transaction - INFO - Transaction 0aa09137-ed31-4373-bc98-467d4b059ce5 started with participants: ['participant_1', 'participant_2']
2025-05-27 18:47:53,969 - system - ERROR - System error in TransactionManager.begin_transaction: Connection failed
2025-05-27 18:47:53,980 - transaction - INFO - Transaction b88573e7-3c17-471e-9f8f-55bbcf2b96b2 started with participants: ['participant_1', 'participant_2']
2025-05-27 18:47:53,981 - transaction - INFO - Transaction b88573e7-3c17-471e-9f8f-55bbcf2b96b2 prepare phase - Participant participant_1: SUCCESS
2025-05-27 18:47:53,982 - transaction - INFO - Transaction b88573e7-3c17-471e-9f8f-55bbcf2b96b2 prepare phase - Participant participant_2: SUCCESS
2025-05-27 18:47:53,982 - transaction - INFO - Transaction b88573e7-3c17-471e-9f8f-55bbcf2b96b2 prepared successfully
2025-05-27 18:47:53,984 - transaction - INFO - Transaction 6063224d-d6c9-486d-b17f-c8ab67d27458 started with participants: ['participant_1', 'participant_2']
2025-05-27 18:47:53,985 - transaction - INFO - Transaction 6063224d-d6c9-486d-b17f-c8ab67d27458 prepare phase - Participant participant_1: FAILED
2025-05-27 18:47:53,986 - system - ERROR - System error in TransactionManager.prepare: Prepare failed for participant_1: Prepare failed
2025-05-27 18:47:53,987 - transaction - INFO - Transaction 6063224d-d6c9-486d-b17f-c8ab67d27458 ROLLBACK
2025-05-27 18:47:53,988 - transaction - INFO - Transaction 6063224d-d6c9-486d-b17f-c8ab67d27458 rolled back successfully
2025-05-27 18:47:53,989 - transaction - INFO - Transaction de381328-d413-407b-a423-3c264ed0f542 started with participants: ['participant_1', 'participant_2']
2025-05-27 18:47:53,990 - transaction - INFO - Transaction de381328-d413-407b-a423-3c264ed0f542 COMMITTED
2025-05-27 18:47:53,991 - transaction - INFO - Transaction de381328-d413-407b-a423-3c264ed0f542 committed successfully
2025-05-27 18:47:53,992 - transaction - INFO - Transaction 7ac9d034-a377-4332-969f-2d287e271a2a started with participants: ['participant_1', 'participant_2']
2025-05-27 18:47:53,993 - transaction - INFO - Transaction 7ac9d034-a377-4332-969f-2d287e271a2a ROLLBACK
2025-05-27 18:47:53,994 - transaction - INFO - Transaction 7ac9d034-a377-4332-969f-2d287e271a2a rolled back successfully
2025-05-27 18:47:53,995 - transaction - INFO - Transaction 10c2c4ea-95e5-4818-b344-34ad73afe4c6 started with participants: ['participant_1', 'participant_2']
2025-05-27 18:47:53,996 - transaction - INFO - Transaction 61d33932-ce48-45f8-bb67-5dc2d903e9eb started with participants: ['participant_1', 'participant_2']
2025-05-27 18:47:53,998 - database - INFO - Database connection Connection pool created for node db1 - localhost:3306
2025-05-27 18:47:53,999 - database - INFO - Database connection Connection pool created for node db2 - localhost:3307
2025-05-27 18:47:54,000 - database - INFO - Database manager initialized with nodes: db1, db2
2025-05-27 18:47:54,002 - database - INFO - Database connection Connection pool created for node db1 - localhost:3306
2025-05-27 18:47:54,003 - database - INFO - Database connection Connection pool created for node db2 - localhost:3307
2025-05-27 18:47:54,004 - database - INFO - Database manager initialized with nodes: db1, db2
2025-05-27 18:47:54,005 - database - INFO - Database connection Connection pool created for node test - localhost:3306
2025-05-27 18:47:54,007 - database - INFO - Database connection Connection pool created for node db1 - localhost:3306
2025-05-27 18:47:54,007 - database - INFO - Database connection Connection pool created for node db2 - localhost:3307
2025-05-27 18:47:54,008 - database - INFO - Database manager initialized with nodes: db1, db2
2025-05-27 18:47:54,009 - database - INFO - Database connection Connection pool created for node test - localhost:3306
